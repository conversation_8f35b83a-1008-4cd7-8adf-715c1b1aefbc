<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="profile-container">
  <div class="profile-card" id="profile-card">
    <div class="profile-header">
      <div class="profile-image-container">
        <img [src]="userProfile()?.avatar || user.avatar || 'assets/images/default-avatar.svg'" alt="Profile Image" class="profile-image" (click)="triggerFileInput()"
             [ngStyle]="{'object-position': 'center ' + ((userProfile()?.avatarPositionY || user.avatarPositionY) || 0) + 'px'}"
             (error)="user.avatar = 'assets/images/default-avatar.svg'">
        <input type="file" #fileInput style="display: none" accept="image/*" (change)="onFileSelected($event)">
        <div class="avatar-actions">
          <div class="avatar-edit-container">
            <button class="edit-avatar-btn" title="Profile picture options">
              <i class="fas fa-camera"></i>
            </button>
            <div class="avatar-dropdown">
              <button class="avatar-dropdown-item" (click)="triggerFileInput()">
                <i class="fas fa-image"></i>
                <span>Upload new picture</span>
              </button>
              <button class="avatar-dropdown-item" (click)="openImagePositionModal()">
                <i class="fas fa-crop-alt"></i>
                <span>Edit photo position</span>
              </button>
              <button class="avatar-dropdown-item remove-option" *ngIf="user.hasCustomAvatar" (click)="removeAvatar()">
                <i class="fas fa-trash"></i>
                <span>Remove picture</span>
              </button>
            </div>
          </div>

        </div>
        <button class="edit-all-btn" (click)="startEditingAll()" *ngIf="!editingAllFields">Edit all</button>
        <div class="edit-all-actions" *ngIf="editingAllFields">
          <button class="save-all-btn" (click)="saveAllFields()">Save All</button>
          <button class="cancel-all-btn" (click)="cancelEditingAll()">Cancel</button>
        </div>
      </div>      <div class="profile-info">
        <h2>{{ userFullName() }}</h2>
        <p class="last-shopping">Last Shopping Date: {{ user.lastShoppingDate }}</p>
      </div>
    </div>

    <div class="profile-content">
      <!-- Full Name -->      
      <div class="profile-field">
        <div class="field-label">
            <span>Full name</span>
            <span *ngIf="!userFullName()" class="field-instruction">Please enter your full name</span>
        </div>
        <div class="field-value-container">
            <div class="field-value" *ngIf="editingField !== 'fullName' && !editingAllFields">
                <span *ngIf="userFullName()">{{ userFullName() }}</span>
                <span *ngIf="!userFullName()" class="placeholder-text">Enter your full name</span>
            </div>
            <div class="field-edit" *ngIf="editingField === 'fullName'">
                <input type="text"
                       [(ngModel)]="tempFieldValue"
                       class="edit-input"
                       placeholder="Enter your full name"
                       #fullNameInput
                       (keyup.enter)="saveField('fullName')">
                <div class="edit-actions">
                    <button class="save-btn" (click)="saveField('fullName')">Save</button>
                    <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
                </div>
            </div>
            <div class="field-edit" *ngIf="editingAllFields && tempUser">
                <input type="text"
                       [(ngModel)]="tempUser.fullName"
                       class="edit-input"
                       placeholder="Enter your full name"
                       #fullNameInputAll
                       (keyup.enter)="saveAllFields()">
            </div>
            <button class="edit-btn" *ngIf="editingField !== 'fullName' && !editingAllFields" (click)="startEditing('fullName')">
                <i class="fas fa-pencil-alt"></i>
            </button>
        </div>
      </div>

      <!-- Birthday -->
      <div class="profile-field">
        <div class="field-label">Birthday</div>
        <div class="field-value-container">
          <div class="field-value" *ngIf="editingField !== 'birthday' && !editingAllFields">
            <span *ngIf="user.birthday">{{ user.birthday }}</span>
            <span *ngIf="!user.birthday" class="placeholder-text">Select your birthday</span>
          </div>
          <div class="field-edit" *ngIf="editingField === 'birthday'">
            <input type="date" [(ngModel)]="tempFieldValue" class="edit-input">
            <div class="edit-actions">
              <button class="save-btn" (click)="saveField('birthday')">Save</button>
              <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
            </div>
          </div>
          <div class="field-edit" *ngIf="editingAllFields && tempUser">
            <input type="date" [(ngModel)]="tempUser.birthday" class="edit-input">
          </div>
          <button class="edit-btn" *ngIf="editingField !== 'birthday' && !editingAllFields" (click)="startEditing('birthday')">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </div>
      </div>      <!-- Email -->
      <div class="profile-field">
        <div class="field-label">E-mail</div>
        <div class="field-value-container">
          <div class="field-value" *ngIf="editingField !== 'email' && !editingAllFields">{{ userEmail() }}</div>
          <div class="field-edit" *ngIf="editingField === 'email'">
            <input type="email" [(ngModel)]="tempFieldValue" class="edit-input">
            <div class="edit-actions">
              <button class="save-btn" (click)="saveField('email')">Save</button>
              <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
            </div>
          </div>
          <div class="field-edit" *ngIf="editingAllFields && tempUser">
            <input type="email" [(ngModel)]="tempUser.email" class="edit-input">
          </div>
          <button class="edit-btn" *ngIf="editingField !== 'email' && !editingAllFields" (click)="startEditing('email')">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </div>
      </div>

      <!-- Phone Number -->
      <div class="profile-field">
        <div class="field-label">Phone number</div>
        <div class="field-value-container">
          <div class="field-value" *ngIf="editingField !== 'phoneNumber' && !editingAllFields">{{ user.phoneNumber || ('+216 00 000 000') }}</div>
          <div class="field-edit" *ngIf="editingField === 'phoneNumber'">
            <div class="phone-input-container">
              <span class="phone-prefix">+216</span>
              <input type="tel"
                     [(ngModel)]="phoneNumberValue"
                     (input)="onPhoneInput($event)"
                     (focus)="onPhoneFocus()"
                     (blur)="onPhoneBlur()"
                     class="edit-input phone-input"
                     [ngClass]="{'error-input': phoneNumberError}"
                     placeholder="00 000 000">
              <div *ngIf="phoneNumberError" class="phone-error-message">
                {{ phoneNumberError }}
              </div>
            </div>
            <div class="edit-actions">
              <button class="save-btn" (click)="saveField('phoneNumber')">Save</button>
              <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
            </div>
          </div>
          <div class="field-edit" *ngIf="editingAllFields && tempUser">
            <div class="phone-input-container">
              <span class="phone-prefix">+216</span>
              <input type="tel"
                     [(ngModel)]="tempPhoneNumberValue"
                     (input)="onPhoneInputAll($event)"
                     class="edit-input phone-input"
                     [ngClass]="{'error-input': tempPhoneNumberError}"
                     placeholder="00 000 000">
              <div *ngIf="tempPhoneNumberError" class="phone-error-message">
                {{ tempPhoneNumberError }}
              </div>
            </div>
          </div>
          <button class="edit-btn" *ngIf="editingField !== 'phoneNumber' && !editingAllFields" (click)="startEditing('phoneNumber')">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </div>
      </div>

      <!-- Governorate & Gender in same line -->
      <div class="profile-field-row">
        <div class="profile-field half-width">
          <div class="field-label">Governorate</div>
          <div class="field-value-container">
            <div class="field-value" *ngIf="editingField !== 'governorate' && !editingAllFields">{{ user.governorate }}</div>
            <div class="field-edit" *ngIf="editingField === 'governorate'">
              <select [(ngModel)]="tempFieldValue" class="edit-input">
                <option value="Ariana">Ariana</option>
                <option value="Béja">Béja</option>
                <option value="Ben Arous">Ben Arous</option>
                <option value="Bizerte">Bizerte</option>
                <option value="Gabès">Gabès</option>
                <option value="Gafsa">Gafsa</option>
                <option value="Jendouba">Jendouba</option>
                <option value="Kairouan">Kairouan</option>
                <option value="Kasserine">Kasserine</option>
                <option value="Kebili">Kebili</option>
                <option value="Kef">Kef</option>
                <option value="Mahdia">Mahdia</option>
                <option value="Manouba">Manouba</option>
                <option value="Medenine">Medenine</option>
                <option value="Monastir">Monastir</option>
                <option value="Nabeul">Nabeul</option>
                <option value="Sfax">Sfax</option>
                <option value="Sidi Bouzid">Sidi Bouzid</option>
                <option value="Siliana">Siliana</option>
                <option value="Sousse">Sousse</option>
                <option value="Tataouine">Tataouine</option>
                <option value="Tozeur">Tozeur</option>
                <option value="Tunis">Tunis</option>
                <option value="Zaghouan">Zaghouan</option>
              </select>
              <div class="edit-actions">
                <button class="save-btn" (click)="saveField('governorate')">Save</button>
                <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
              </div>
            </div>
            <div class="field-edit" *ngIf="editingAllFields && tempUser">
              <select [(ngModel)]="tempUser.governorate" class="edit-input">
                <option value="Ariana">Ariana</option>
                <option value="Béja">Béja</option>
                <option value="Ben Arous">Ben Arous</option>
                <option value="Bizerte">Bizerte</option>
                <option value="Gabès">Gabès</option>
                <option value="Gafsa">Gafsa</option>
                <option value="Jendouba">Jendouba</option>
                <option value="Kairouan">Kairouan</option>
                <option value="Kasserine">Kasserine</option>
                <option value="Kebili">Kebili</option>
                <option value="Kef">Kef</option>
                <option value="Mahdia">Mahdia</option>
                <option value="Manouba">Manouba</option>
                <option value="Medenine">Medenine</option>
                <option value="Monastir">Monastir</option>
                <option value="Nabeul">Nabeul</option>
                <option value="Sfax">Sfax</option>
                <option value="Sidi Bouzid">Sidi Bouzid</option>
                <option value="Siliana">Siliana</option>
                <option value="Sousse">Sousse</option>
                <option value="Tataouine">Tataouine</option>
                <option value="Tozeur">Tozeur</option>
                <option value="Tunis">Tunis</option>
                <option value="Zaghouan">Zaghouan</option>
              </select>
            </div>
            <button class="edit-btn" *ngIf="editingField !== 'governorate' && !editingAllFields" (click)="startEditing('governorate')">
              <i class="fas fa-pencil-alt"></i>
            </button>
          </div>
        </div>

        <div class="profile-field half-width">
          <div class="field-label">Gender</div>
          <div class="field-value-container">
            <div class="field-value" *ngIf="editingField !== 'gender' && !editingAllFields">
              <span *ngIf="user.gender">{{ user.gender }}</span>
              <span *ngIf="!user.gender && user.showGenderPlaceholder" class="placeholder-text">Select your gender</span>
            </div>
            <div class="field-edit" *ngIf="editingField === 'gender'">
              <select [(ngModel)]="tempFieldValue" class="edit-input">
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
              <div class="edit-actions">
                <button class="save-btn" (click)="saveField('gender')">Save</button>
                <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
              </div>
            </div>
            <div class="field-edit" *ngIf="editingAllFields && tempUser">
              <select [(ngModel)]="tempUser.gender" class="edit-input">
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
            </div>
            <button class="edit-btn" *ngIf="editingField !== 'gender' && !editingAllFields" (click)="startEditing('gender')">
              <i class="fas fa-pencil-alt"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Address -->
      <div class="profile-field">
        <div class="field-label">Address</div>
        <div class="field-value-container address-field-container address-field-value-container">
          <div class="field-value" *ngIf="editingField !== 'address' && !editingAllFields">
            <span *ngIf="user.address">{{ user.address }}</span>
            <span *ngIf="!user.address && user.showAddressPlaceholder" class="placeholder-text">Enter your address</span>
          </div>
          <div class="field-edit" *ngIf="editingField === 'address'">
            <input type="text" [(ngModel)]="tempFieldValue" class="edit-input">
            <div class="edit-actions">
              <button class="save-btn" (click)="saveField('address')">Save</button>
              <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
            </div>
          </div>
          <div class="field-edit" *ngIf="editingAllFields && tempUser">
            <input type="text" [(ngModel)]="tempUser.address" class="edit-input">
          </div>
          <button class="edit-btn" *ngIf="editingField !== 'address' && !editingAllFields" (click)="startEditing('address')">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </div>
      </div>

      <!-- Occupation -->
      <div class="profile-field">
        <div class="field-label">Occupation</div>
        <div class="field-value-container occupation-field-container occupation-field-value-container">
            <div class="field-value" *ngIf="editingField !== 'role' && !editingAllFields">
                <span *ngIf="user.occupationType">{{ user.occupationType }}{{ user.occupationPlace ? ' at ' + user.occupationPlace : '' }}</span>
                <span *ngIf="!user.occupationType && user.showOccupationPlaceholder" class="placeholder-text">Select your occupation</span>
            </div>
            <div class="field-edit occupation-edit" *ngIf="editingField === 'role'">
                <div class="occupation-inputs">
                    <div class="occupation-type">
                        <label>Occupation Type</label>
                        <select [(ngModel)]="occupationType" class="edit-input" (change)="occupationPlace = ''">
                            <option value="Working">Working</option>
                            <option value="Study">Study</option>
                            <option value="Train">Train</option>
                            <option value="Unemployed">Unemployed</option>
                        </select>
                    </div>

                    <div class="occupation-place" *ngIf="occupationType && occupationType !== 'Unemployed'">
                        <label>Where do you {{ occupationType === 'Working' ? 'work' : occupationType === 'Study' ? 'study' : 'train' }}?</label>
                        <input type="text" [(ngModel)]="occupationPlace" class="edit-input"
                               [placeholder]="'Enter where you ' + (occupationType === 'Working' ? 'work' : occupationType === 'Study' ? 'study' : 'train')">
                    </div>
                </div>

                <div class="edit-actions">
                    <button class="save-btn" (click)="saveField('role')" [disabled]="!occupationType || (occupationType !== 'Unemployed' && !occupationPlace)">Save</button>
                    <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
                </div>
            </div>
            <div class="field-edit occupation-edit" *ngIf="editingAllFields && tempUser">
                <div class="occupation-inputs">
                    <div class="occupation-type">
                        <label>Occupation Type</label>
                        <select [(ngModel)]="tempOccupationType" class="edit-input" (change)="tempUser.occupationPlace = ''">
                            <option value="Working">Working</option>
                            <option value="Study">Study</option>
                            <option value="Train">Train</option>
                            <option value="Unemployed">Unemployed</option>
                        </select>
                    </div>

                    <div class="occupation-place" *ngIf="tempOccupationType && tempOccupationType !== 'Unemployed'">
                        <label>Where do you {{ tempOccupationType === 'Working' ? 'work' : tempOccupationType === 'Study' ? 'study' : 'train' }}?</label>
                        <input type="text" [(ngModel)]="tempUser.occupationPlace" class="edit-input"
                               [placeholder]="'Enter where you ' + (tempOccupationType === 'Working' ? 'work' : tempOccupationType === 'Study' ? 'study' : 'train')">
                    </div>
                </div>
            </div>
            <button class="edit-btn" *ngIf="editingField !== 'role' && !editingAllFields" (click)="startEditing('role')">
                <i class="fas fa-pencil-alt"></i>
            </button>
        </div>
      </div>
    </div>


  </div>

<!-- Image Repositioning Modal -->
<div class="image-repositioning-modal" *ngIf="showImageRepositionModal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>Adjust Your Profile Picture</h3>
      <button class="close-modal-btn" (click)="cancelImageRepositioning()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="modal-body">
      <div class="image-container"
           (mousedown)="startDragging($event)">
        <div class="image-preview-container">
          <img [src]="tempImageSrc" alt="Profile Image Preview"
               class="image-preview"
               [class.grabbing]="isDragging"
               #previewImage>
          <div class="image-mask"></div>
        </div>
      </div>
      <div class="reposition-instructions">
        <p>Drag the image up or down to adjust how it appears in your profile</p>
      </div>
    </div>
    <div class="modal-footer">
      <button class="save-image-btn" (click)="saveRepositionedImage()">Save</button>
      <button class="cancel-btn" (click)="cancelImageRepositioning()">Cancel</button>
    </div>
  </div>
</div>
</div>

